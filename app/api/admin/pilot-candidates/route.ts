import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { 
  getPilotEligibleCandidates, 
  getCurrentPilotCandidates,
  applyPilotSettings,
  removePilotSettings,
  PilotSettings
} from '@/lib/pilot-settings';

// GET - Retrieve pilot candidates
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organizationId');
    const type = searchParams.get('type') || 'eligible'; // 'eligible' or 'current'

    if (!organizationId) {
      return NextResponse.json(
        { error: 'organizationId is required' }, 
        { status: 400 }
      );
    }

    let candidates;
    if (type === 'current') {
      candidates = await getCurrentPilotCandidates(organizationId);
    } else {
      candidates = await getPilotEligibleCandidates(organizationId);
    }

    return NextResponse.json({
      candidates,
      total: candidates.length,
      type
    });

  } catch (error) {
    console.error('Error fetching pilot candidates:', error);
    return NextResponse.json(
      { error: 'Failed to fetch candidates', details: error.message },
      { status: 500 }
    );
  }
}

// POST - Apply pilot settings to candidates
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { 
      action,
      careerPracticeIds, 
      settings 
    } = body;

    if (!careerPracticeIds || !Array.isArray(careerPracticeIds)) {
      return NextResponse.json(
        { error: 'careerPracticeIds array is required' }, 
        { status: 400 }
      );
    }

    let results;
    
    if (action === 'remove') {
      results = await removePilotSettings(careerPracticeIds);
      return NextResponse.json({
        message: `Pilot settings removed from ${results.success} candidates`,
        success_count: results.success,
        failed_count: results.failed.length,
        failed_details: results.failed
      });
    } else {
      // Default action is 'apply'
      if (!settings) {
        return NextResponse.json(
          { error: 'settings object is required for apply action' }, 
          { status: 400 }
        );
      }

      const pilotSettings: PilotSettings = {
        llm_provider: settings.llm_provider || 'google',
        video_avatar: settings.video_avatar || null,
        interview_round: settings.interview_round || 'technical'
      };

      results = await applyPilotSettings(careerPracticeIds, pilotSettings, session.userId);
      
      return NextResponse.json({
        message: `Pilot settings applied to ${results.success} candidates`,
        success_count: results.success,
        failed_count: results.failed.length,
        failed_details: results.failed,
        applied_settings: pilotSettings
      });
    }

  } catch (error) {
    console.error('Error managing pilot candidates:', error);
    return NextResponse.json(
      { error: 'Failed to manage pilot candidates', details: error.message },
      { status: 500 }
    );
  }
}
