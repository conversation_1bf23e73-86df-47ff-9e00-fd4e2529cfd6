import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/prisma/db';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { 
      careerPracticeIds, 
      llm_provider, 
      video_avatar, 
      interview_round 
    } = body;

    if (!careerPracticeIds || !Array.isArray(careerPracticeIds)) {
      return NextResponse.json(
        { error: 'careerPracticeIds array is required' }, 
        { status: 400 }
      );
    }

    const pilotSettings = {
      llm_provider: llm_provider || 'google',
      video_avatar: video_avatar || null,
      interview_round: interview_round || 'technical',
      enabled_by: session.userId,
      enabled_at: new Date().toISOString()
    };

    // Update multiple career practice records
    const updatePromises = careerPracticeIds.map(async (id: string) => {
      const existingRecord = await db.careerPractice.findUnique({
        where: { id },
        select: { timing: true, comments: true }
      });

      if (!existingRecord) {
        throw new Error(`CareerPractice with id ${id} not found`);
      }

      // Store in timing field for now (you can change this to comments if preferred)
      const updatedTiming = {
        ...existingRecord.timing,
        pilot_settings: pilotSettings
      };

      return db.careerPractice.update({
        where: { id },
        data: {
          timing: updatedTiming
        }
      });
    });

    const results = await Promise.all(updatePromises);

    return NextResponse.json({
      message: `Pilot settings applied to ${results.length} candidates`,
      updated_count: results.length,
      pilot_settings: pilotSettings
    });

  } catch (error) {
    console.error('Error setting pilot settings:', error);
    return NextResponse.json(
      { error: 'Failed to set pilot settings', details: error.message },
      { status: 500 }
    );
  }
}

// GET endpoint to retrieve current pilot settings
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const careerPracticeId = searchParams.get('id');

    if (!careerPracticeId) {
      return NextResponse.json(
        { error: 'careerPracticeId is required' }, 
        { status: 400 }
      );
    }

    const careerPractice = await db.careerPractice.findUnique({
      where: { id: careerPracticeId },
      select: { 
        timing: true, 
        comments: true,
        eventDetails: {
          select: {
            round: true
          }
        }
      }
    });

    if (!careerPractice) {
      return NextResponse.json(
        { error: 'CareerPractice not found' }, 
        { status: 404 }
      );
    }

    const pilotSettings = careerPractice?.timing?.pilot_settings || 
                         careerPractice?.comments?.pilot_settings || 
                         null;

    return NextResponse.json({
      pilot_settings: pilotSettings,
      default_round: careerPractice?.eventDetails?.round,
      has_pilot_settings: !!pilotSettings
    });

  } catch (error) {
    console.error('Error getting pilot settings:', error);
    return NextResponse.json(
      { error: 'Failed to get pilot settings', details: error.message },
      { status: 500 }
    );
  }
}
