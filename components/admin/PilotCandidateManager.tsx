'use client';

import React, { useState, useEffect } from 'react';

interface PilotCandidate {
  careerPracticeId: string;
  userId: string;
  email: string;
  name: string;
  role: string;
  level: string;
  eventName: string;
  pilotSettings?: {
    llm_provider?: string;
    video_avatar?: string;
    interview_round?: string;
    enabled_by?: string;
    enabled_at?: string;
  };
}

interface PilotCandidateManagerProps {
  organizationId: string;
}

export default function PilotCandidateManager({ organizationId }: PilotCandidateManagerProps) {
  const [candidates, setCandidates] = useState<PilotCandidate[]>([]);
  const [selectedCandidates, setSelectedCandidates] = useState<string[]>([]);
  const [viewType, setViewType] = useState<'eligible' | 'current'>('eligible');
  const [loading, setLoading] = useState(false);
  const [settings, setSettings] = useState({
    llm_provider: 'google',
    video_avatar: '',
    interview_round: 'technical'
  });

  const fetchCandidates = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `/api/admin/pilot-candidates?organizationId=${organizationId}&type=${viewType}`
      );
      const data = await response.json();
      setCandidates(data.candidates || []);
    } catch (error) {
      console.error('Error fetching candidates:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (organizationId) {
      fetchCandidates();
    }
  }, [organizationId, viewType]);

  const handleApplySettings = async () => {
    if (selectedCandidates.length === 0) {
      alert('Please select at least one candidate');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/admin/pilot-candidates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'apply',
          careerPracticeIds: selectedCandidates,
          settings
        }),
      });

      const result = await response.json();
      if (response.ok) {
        alert(`Success: ${result.message}`);
        setSelectedCandidates([]);
        fetchCandidates();
      } else {
        alert(`Error: ${result.error}`);
      }
    } catch (error) {
      console.error('Error applying settings:', error);
      alert('Failed to apply settings');
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveSettings = async () => {
    if (selectedCandidates.length === 0) {
      alert('Please select at least one candidate');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/admin/pilot-candidates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'remove',
          careerPracticeIds: selectedCandidates
        }),
      });

      const result = await response.json();
      if (response.ok) {
        alert(`Success: ${result.message}`);
        setSelectedCandidates([]);
        fetchCandidates();
      } else {
        alert(`Error: ${result.error}`);
      }
    } catch (error) {
      console.error('Error removing settings:', error);
      alert('Failed to remove settings');
    } finally {
      setLoading(false);
    }
  };

  const toggleCandidateSelection = (candidateId: string) => {
    setSelectedCandidates(prev => 
      prev.includes(candidateId) 
        ? prev.filter(id => id !== candidateId)
        : [...prev, candidateId]
    );
  };

  const selectAll = () => {
    setSelectedCandidates(candidates.map(c => c.careerPracticeId));
  };

  const clearSelection = () => {
    setSelectedCandidates([]);
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <h2 className="text-2xl font-bold mb-6">AI Interview Pilot Management</h2>
      
      {/* View Toggle */}
      <div className="mb-4">
        <div className="flex space-x-4">
          <button
            onClick={() => setViewType('eligible')}
            className={`px-4 py-2 rounded ${
              viewType === 'eligible' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700'
            }`}
          >
            Eligible Candidates ({candidates.length})
          </button>
          <button
            onClick={() => setViewType('current')}
            className={`px-4 py-2 rounded ${
              viewType === 'current' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700'
            }`}
          >
            Current Pilot Candidates
          </button>
        </div>
      </div>

      {/* Pilot Settings Form */}
      {viewType === 'eligible' && (
        <div className="bg-gray-50 p-4 rounded-lg mb-6">
          <h3 className="text-lg font-semibold mb-4">Pilot Settings</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">LLM Provider</label>
              <select
                value={settings.llm_provider}
                onChange={(e) => setSettings(prev => ({ ...prev, llm_provider: e.target.value }))}
                className="w-full p-2 border rounded"
              >
                <option value="google">Google (Gemini)</option>
                <option value="openai">OpenAI (GPT)</option>
                <option value="anthropic">Anthropic (Claude)</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Video Avatar</label>
              <input
                type="text"
                value={settings.video_avatar}
                onChange={(e) => setSettings(prev => ({ ...prev, video_avatar: e.target.value }))}
                placeholder="Avatar ID or URL"
                className="w-full p-2 border rounded"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Interview Round</label>
              <select
                value={settings.interview_round}
                onChange={(e) => setSettings(prev => ({ ...prev, interview_round: e.target.value }))}
                className="w-full p-2 border rounded"
              >
                <option value="technical">Technical</option>
                <option value="hr">HR</option>
                <option value="managerial">Managerial</option>
                <option value="behavioral">Behavioral</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex space-x-4 mb-6">
        <button
          onClick={selectAll}
          className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          disabled={loading}
        >
          Select All
        </button>
        <button
          onClick={clearSelection}
          className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          disabled={loading}
        >
          Clear Selection
        </button>
        {viewType === 'eligible' && (
          <button
            onClick={handleApplySettings}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
            disabled={loading || selectedCandidates.length === 0}
          >
            Apply Pilot Settings ({selectedCandidates.length})
          </button>
        )}
        {viewType === 'current' && (
          <button
            onClick={handleRemoveSettings}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
            disabled={loading || selectedCandidates.length === 0}
          >
            Remove from Pilot ({selectedCandidates.length})
          </button>
        )}
      </div>

      {/* Candidates Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full bg-white border border-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-4 py-2 border-b">
                <input
                  type="checkbox"
                  checked={selectedCandidates.length === candidates.length && candidates.length > 0}
                  onChange={selectedCandidates.length === candidates.length ? clearSelection : selectAll}
                />
              </th>
              <th className="px-4 py-2 border-b text-left">Name</th>
              <th className="px-4 py-2 border-b text-left">Email</th>
              <th className="px-4 py-2 border-b text-left">Role</th>
              <th className="px-4 py-2 border-b text-left">Level</th>
              <th className="px-4 py-2 border-b text-left">Event</th>
              {viewType === 'current' && (
                <th className="px-4 py-2 border-b text-left">Pilot Settings</th>
              )}
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={viewType === 'current' ? 7 : 6} className="px-4 py-8 text-center">
                  Loading...
                </td>
              </tr>
            ) : candidates.length === 0 ? (
              <tr>
                <td colSpan={viewType === 'current' ? 7 : 6} className="px-4 py-8 text-center text-gray-500">
                  No candidates found
                </td>
              </tr>
            ) : (
              candidates.map((candidate) => (
                <tr key={candidate.careerPracticeId} className="hover:bg-gray-50">
                  <td className="px-4 py-2 border-b">
                    <input
                      type="checkbox"
                      checked={selectedCandidates.includes(candidate.careerPracticeId)}
                      onChange={() => toggleCandidateSelection(candidate.careerPracticeId)}
                    />
                  </td>
                  <td className="px-4 py-2 border-b">{candidate.name}</td>
                  <td className="px-4 py-2 border-b">{candidate.email}</td>
                  <td className="px-4 py-2 border-b">{candidate.role}</td>
                  <td className="px-4 py-2 border-b">{candidate.level}</td>
                  <td className="px-4 py-2 border-b">{candidate.eventName}</td>
                  {viewType === 'current' && (
                    <td className="px-4 py-2 border-b">
                      {candidate.pilotSettings ? (
                        <div className="text-sm">
                          <div>LLM: {candidate.pilotSettings.llm_provider}</div>
                          <div>Round: {candidate.pilotSettings.interview_round}</div>
                          {candidate.pilotSettings.video_avatar && (
                            <div>Avatar: {candidate.pilotSettings.video_avatar}</div>
                          )}
                        </div>
                      ) : (
                        <span className="text-gray-400">No settings</span>
                      )}
                    </td>
                  )}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}
