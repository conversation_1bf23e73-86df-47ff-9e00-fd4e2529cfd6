import { db } from '@/prisma/db';

export interface PilotSettings {
  llm_provider?: 'google' | 'openai' | 'anthropic';
  video_avatar?: string | null;
  interview_round?: string;
  enabled_by?: string;
  enabled_at?: string;
}

export interface PilotCandidate {
  careerPracticeId: string;
  userId: string;
  email: string;
  name: string;
  role: string;
  level: string;
  eventName: string;
  pilotSettings?: PilotSettings;
}

/**
 * Get all candidates eligible for pilot testing
 */
export async function getPilotEligibleCandidates(organizationId: string): Promise<PilotCandidate[]> {
  const candidates = await db.careerPractice.findMany({
    where: {
      eventDetails: {
        organizationId: organizationId,
        isAiQuestion: true, // Only AI interviews
      },
      interviewStatus: {
        in: ['NOT_STARTED', 'PARTIALLY_COMPLETED']
      }
    },
    include: {
      user: {
        select: {
          email: true,
          name: true
        }
      },
      eventDetails: {
        select: {
          name: true,
          round: true
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    }
  });

  return candidates.map(candidate => ({
    careerPracticeId: candidate.id,
    userId: candidate.userId,
    email: candidate.user?.email || '',
    name: candidate.user?.name || '',
    role: candidate.role,
    level: candidate.level,
    eventName: candidate.eventDetails?.name || candidate.event || '',
    pilotSettings: candidate.timing?.pilot_settings || candidate.comments?.pilot_settings || null
  }));
}

/**
 * Get candidates currently in pilot program
 */
export async function getCurrentPilotCandidates(organizationId: string): Promise<PilotCandidate[]> {
  const candidates = await db.careerPractice.findMany({
    where: {
      eventDetails: {
        organizationId: organizationId,
        isAiQuestion: true,
      },
      OR: [
        {
          timing: {
            path: ['pilot_settings'],
            not: null
          }
        },
        {
          comments: {
            path: ['pilot_settings'],
            not: null
          }
        }
      ]
    },
    include: {
      user: {
        select: {
          email: true,
          name: true
        }
      },
      eventDetails: {
        select: {
          name: true,
          round: true
        }
      }
    },
    orderBy: {
      updatedAt: 'desc'
    }
  });

  return candidates.map(candidate => ({
    careerPracticeId: candidate.id,
    userId: candidate.userId,
    email: candidate.user?.email || '',
    name: candidate.user?.name || '',
    role: candidate.role,
    level: candidate.level,
    eventName: candidate.eventDetails?.name || candidate.event || '',
    pilotSettings: candidate.timing?.pilot_settings || candidate.comments?.pilot_settings || null
  }));
}

/**
 * Apply pilot settings to specific candidates
 */
export async function applyPilotSettings(
  careerPracticeIds: string[], 
  settings: PilotSettings,
  userId: string
): Promise<{ success: number; failed: string[] }> {
  const results = { success: 0, failed: [] as string[] };
  
  const pilotSettings = {
    ...settings,
    enabled_by: userId,
    enabled_at: new Date().toISOString()
  };

  for (const id of careerPracticeIds) {
    try {
      const existingRecord = await db.careerPractice.findUnique({
        where: { id },
        select: { timing: true }
      });

      if (!existingRecord) {
        results.failed.push(`${id}: Record not found`);
        continue;
      }

      const updatedTiming = {
        ...existingRecord.timing,
        pilot_settings: pilotSettings
      };

      await db.careerPractice.update({
        where: { id },
        data: { timing: updatedTiming }
      });

      results.success++;
    } catch (error) {
      results.failed.push(`${id}: ${error.message}`);
    }
  }

  return results;
}

/**
 * Remove pilot settings from candidates
 */
export async function removePilotSettings(careerPracticeIds: string[]): Promise<{ success: number; failed: string[] }> {
  const results = { success: 0, failed: [] as string[] };

  for (const id of careerPracticeIds) {
    try {
      const existingRecord = await db.careerPractice.findUnique({
        where: { id },
        select: { timing: true, comments: true }
      });

      if (!existingRecord) {
        results.failed.push(`${id}: Record not found`);
        continue;
      }

      // Remove pilot_settings from timing
      const updatedTiming = { ...existingRecord.timing };
      delete updatedTiming.pilot_settings;

      // Remove pilot_settings from comments if it exists
      const updatedComments = { ...existingRecord.comments };
      delete updatedComments?.pilot_settings;

      await db.careerPractice.update({
        where: { id },
        data: { 
          timing: updatedTiming,
          comments: updatedComments
        }
      });

      results.success++;
    } catch (error) {
      results.failed.push(`${id}: ${error.message}`);
    }
  }

  return results;
}

/**
 * Get pilot settings for a specific candidate
 */
export async function getPilotSettings(careerPracticeId: string): Promise<PilotSettings | null> {
  const careerPractice = await db.careerPractice.findUnique({
    where: { id: careerPracticeId },
    select: { timing: true, comments: true }
  });

  if (!careerPractice) {
    return null;
  }

  return careerPractice.timing?.pilot_settings || 
         careerPractice.comments?.pilot_settings || 
         null;
}
